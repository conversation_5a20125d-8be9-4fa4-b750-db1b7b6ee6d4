# Use Node.js 18 Alpine (mais est<PERSON>)
FROM node:18-alpine

# Definir diretório de trabalho
WORKDIR /app

# Instalar dependências do sistema necessárias
RUN apk add --no-cache python3 make g++

# Copiar arquivos de dependências
COPY package*.json ./

# Instalar dependências de produção
RUN npm ci --only=production && npm cache clean --force

# Copiar código fonte
COPY . .

# Pular build do dashboard por enquanto (deve ser buildado antes do deploy)
RUN echo "Skipping dashboard build - should be pre-built"

# Expor porta
EXPOSE 8080

# Definir variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=8080

# Comando para iniciar a aplicação
CMD ["npm", "start"]
